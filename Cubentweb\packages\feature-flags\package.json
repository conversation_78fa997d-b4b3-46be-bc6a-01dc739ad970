{"name": "@repo/feature-flags", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/design-system": "workspace:*", "@t3-oss/env-nextjs": "^0.13.4", "@vercel/toolbar": "^0.1.37", "flags": "^4.0.1", "react": "^19.1.0", "zod": "^3.25.28"}, "peerDependencies": {"next": "15.1.7"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3"}}