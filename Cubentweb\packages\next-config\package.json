{"name": "@repo/next-config", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "next": "15.3.2"}, "dependencies": {"@next/bundle-analyzer": "15.3.2", "@prisma/nextjs-monorepo-workaround-plugin": "^6.8.2", "@t3-oss/env-core": "^0.13.4", "@t3-oss/env-nextjs": "^0.13.4", "zod": "^3.25.28"}}