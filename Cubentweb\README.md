# 🤖 Cubent Coder

**AI-Powered Autonomous Coding Agent for Visual Studio Code**

🚀 VS Code Extension Integration Ready!

<div>
  <img src="https://img.shields.io/visual-studio-marketplace/d/cubent.cubent" alt="Downloads" />
  <img src="https://img.shields.io/visual-studio-marketplace/v/cubent.cubent" alt="Version" />
  <img src="https://img.shields.io/github/license/LaxBloxBoy2/Cubent" alt="License" />
  <img src="https://img.shields.io/discord/**********" alt="Discord" />
</div>

## Overview

**Cubent Coder** is an AI-powered autonomous coding agent that lives in your VS Code editor. It can communicate in natural language, read and write files directly in your workspace, run terminal commands, automate browser actions, and integrate with any OpenAI-compatible or custom API/model.

Whether you're seeking a flexible coding partner, a system architect, or specialized roles like a QA engineer or product manager, Cubent Coder can help you build software more efficiently.

## Key Features

- 🚀 **Generate Code** from natural language descriptions
- 🔧 **Refactor & Debug** existing code
- 📝 **Write & Update** documentation
- 🤔 **Answer Questions** about your codebase
- 🔄 **Automate** repetitive tasks
- 🏗️ **Create** new files and projects
- 🎭 **Multiple Modes** (Chat, Plan, Agent, Custom)
- 🛠️ **Smart Tools** for file operations, terminal commands, and browser automation

## Getting Started

1. [Install Cubent Coder](https://marketplace.visualstudio.com/items?itemName=cubent.cubent) from VS Code Marketplace
2. Connect your AI provider (OpenAI, Anthropic, or custom)
3. Start coding with AI assistance

For detailed setup instructions, visit our [documentation](https://docs.cubent.com).

## Contributors

<a href="https://github.com/vercel/next-forge/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=vercel/next-forge" />
</a>

Made with [contrib.rocks](https://contrib.rocks).
