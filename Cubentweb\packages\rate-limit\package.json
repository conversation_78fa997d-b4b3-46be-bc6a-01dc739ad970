{"name": "@repo/rate-limit", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@t3-oss/env-nextjs": "^0.13.4", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.3"}}