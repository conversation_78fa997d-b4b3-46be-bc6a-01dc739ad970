{"name": "@repo/notifications", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@knocklabs/node": "^1.3.0", "@knocklabs/react": "^0.7.11", "@t3-oss/env-nextjs": "^0.13.4", "react": "^19.1.0", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.3", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5"}}