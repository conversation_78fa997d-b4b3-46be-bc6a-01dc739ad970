---
title: Discord Community
description: 'Join the Cubent Coder Discord community'
---

## Welcome to Our Discord

Join thousands of developers in the official Cubent Coder Discord community! Get real-time help, share your projects, and connect with fellow developers using AI-powered coding tools.

<Card
  title="Join Discord Server"
  icon="discord"
  href="https://discord.gg/cubent"
>
  Click here to join our Discord community
</Card>

## What You'll Find

### 🆘 **Support Channels**

- **#general-help**: General questions and troubleshooting
- **#installation-setup**: Help with installation and configuration
- **#api-providers**: Discussions about different AI providers
- **#custom-modes**: Share and discuss custom AI modes

### 💬 **Discussion Channels**

- **#general**: General community chat
- **#showcase**: Show off your projects and achievements
- **#feature-requests**: Suggest new features and improvements
- **#tips-and-tricks**: Share useful tips and workflows

### 🔧 **Development Channels**

- **#development**: Discussions about Cubent Coder development
- **#beta-testing**: Early access to new features
- **#extensions**: Third-party extensions and integrations
- **#api-discussion**: API and integration discussions

### 📢 **Announcement Channels**

- **#announcements**: Official updates and releases
- **#changelog**: Detailed change logs and updates
- **#events**: Community events and workshops

## Community Guidelines

### Be Respectful

- Treat all community members with respect
- Use inclusive language and be welcoming to newcomers
- Avoid spam, self-promotion, or off-topic discussions
- Keep conversations constructive and helpful

### Getting Help

When asking for help:

1. **Search first**: Check if your question has been answered
2. **Use the right channel**: Post in the appropriate channel
3. **Provide context**: Include relevant details about your issue
4. **Share code**: Use code blocks for better readability
5. **Be patient**: Community members volunteer their time

### Sharing Code

When sharing code snippets:

```markdown
Use code blocks with language specification:

```javascript
function example() {
  console.log("Hello, world!");
}
```

For longer code, use:
- GitHub Gists
- Pastebin
- CodePen/JSFiddle for web examples
```

## Getting Started

### 1. Introduce Yourself

Head to **#general** and introduce yourself:
- What kind of development do you do?
- What are you hoping to achieve with Cubent Coder?
- Any specific challenges you're facing?

### 2. Set Up Your Profile

- Add a profile picture
- Set a descriptive username
- Add relevant roles (React Developer, Python Developer, etc.)

### 3. Explore Channels

Browse different channels to get a feel for the community:
- Read pinned messages for important information
- Check out recent discussions
- See what others are building

## Community Events

### Weekly Events

- **Monday**: Feature spotlight and demos
- **Wednesday**: Community Q&A sessions
- **Friday**: Show and tell - share your projects

### Monthly Events

- **First Saturday**: Virtual meetup and networking
- **Third Thursday**: Developer workshop
- **Last Friday**: Community feedback session

### Special Events

- **Release parties**: Celebrate major version releases
- **Hackathons**: Build projects together
- **AMAs**: Ask Me Anything sessions with the team
- **Workshops**: Deep-dive technical sessions

## Roles and Recognition

### Community Roles

Earn roles based on your participation:

- **🆕 Newcomer**: New to the community
- **💬 Active Member**: Regular participant
- **🎯 Helper**: Frequently helps others
- **⭐ Contributor**: Contributes to the project
- **🏆 Expert**: Recognized expert in specific areas

### Recognition Programs

- **Helper of the Month**: Recognize outstanding community helpers
- **Project Spotlight**: Feature amazing community projects
- **Contributor Awards**: Acknowledge code and documentation contributions

## Resources and Links

### Quick Links

- **Documentation**: [docs.cubent.com](https://docs.cubent.com)
- **GitHub Repository**: [github.com/LaxBloxBoy2/Cubent](https://github.com/LaxBloxBoy2/Cubent)
- **VS Code Marketplace**: [Cubent Coder Extension](https://marketplace.visualstudio.com/items?itemName=cubent.cubent)
- **Reddit Community**: [r/cubent](https://www.reddit.com/r/cubent/)

### Useful Bots

- **Cubent Bot**: Get quick help and information
- **GitHub Bot**: Track issues and pull requests
- **Docs Bot**: Search documentation quickly

## Moderation and Support

### Community Moderators

Our volunteer moderators help maintain a positive environment:
- Enforce community guidelines
- Help resolve conflicts
- Assist with technical questions
- Organize community events

### Reporting Issues

If you encounter problems:
1. Use the **#support** channel for technical issues
2. Contact moderators for community issues
3. Use Discord's built-in reporting for serious violations

### Appeals Process

If you disagree with a moderation decision:
1. Contact the moderator privately
2. If unresolved, escalate to senior moderators
3. Final appeals go to the community team

## Contributing to the Community

### Ways to Help

- **Answer questions**: Help newcomers get started
- **Share knowledge**: Write tutorials and guides
- **Test features**: Try beta releases and provide feedback
- **Organize events**: Help plan community activities
- **Moderate**: Apply to become a community moderator

### Content Creation

Share your knowledge:
- **Tutorials**: Step-by-step guides
- **Videos**: Screen recordings and walkthroughs
- **Blog posts**: In-depth articles and case studies
- **Templates**: Reusable project templates

### Feedback and Suggestions

Help improve Cubent Coder:
- Report bugs and issues
- Suggest new features
- Provide usability feedback
- Share use cases and workflows

## Discord Etiquette

### Channel Usage

- **Stay on topic**: Use appropriate channels
- **Search before asking**: Check recent messages
- **Use threads**: Keep discussions organized
- **Pin important info**: Help others find key information

### Communication Tips

- **Be clear and concise**: Make your questions easy to understand
- **Use mentions sparingly**: Don't spam @everyone or @here
- **Respect time zones**: Be patient with responses
- **Say thanks**: Acknowledge help from others

### Code Sharing

- **Use code blocks**: Format code properly
- **Include context**: Explain what you're trying to do
- **Share minimal examples**: Focus on the specific issue
- **Provide error messages**: Include full error text

## Success Stories

### Community Highlights

- **10,000+ members**: Growing community of developers
- **500+ daily messages**: Active discussions and help
- **50+ community projects**: Amazing creations shared
- **95% positive feedback**: High satisfaction ratings

### Member Testimonials

> "The Discord community helped me get started with Cubent Coder in just one day. The support is incredible!" - @developer123

> "I've learned so much from the community. The tips and tricks channel is pure gold!" - @coder_jane

> "Found my new favorite development tool thanks to this community. The AI integration is game-changing!" - @tech_enthusiast

## Next Steps

Ready to join the community?

<CardGroup cols={2}>
  <Card
    title="Join Discord"
    icon="discord"
    href="https://discord.gg/cubent"
  >
    Join our Discord server now
  </Card>
  <Card
    title="Contributing Guide"
    icon="heart"
    href="/community/contributing"
  >
    Learn how to contribute to the project
  </Card>
</CardGroup>
