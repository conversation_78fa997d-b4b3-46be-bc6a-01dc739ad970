import { analytics } from '@repo/analytics/posthog/server';
import { auth } from '@repo/auth/server';
import { flag } from 'flags/next';

export const createFlag = (key: string) =>
  flag({
    key,
    defaultValue: false,
    async decide() {
      const { userId } = await auth();

      if (!userId) {
        return this.defaultValue as boolean;
      }

      const isEnabled = analytics
        ? await analytics.isFeatureEnabled(key, userId)
        : null;

      return isEnabled ?? (this.defaultValue as boolean);
    },
  });
