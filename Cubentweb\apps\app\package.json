{"name": "app", "private": true, "scripts": {"dev": "next dev -p 3000 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true npm run build", "test": "NODE_ENV=test vitest run", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/collaboration": "workspace:*", "@repo/database": "workspace:*", "@repo/design-system": "workspace:*", "@repo/feature-flags": "workspace:*", "@repo/next-config": "workspace:*", "@repo/notifications": "workspace:*", "@repo/observability": "workspace:*", "@repo/security": "workspace:*", "@repo/seo": "workspace:*", "@repo/webhooks": "workspace:*", "@sentry/nextjs": "^9.22.0", "@t3-oss/env-nextjs": "^0.13.4", "fuse.js": "^7.1.0", "import-in-the-middle": "^1.13.2", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "require-in-the-middle": "^7.5.2", "sonner": "^2.0.3", "zod": "^3.25.28"}, "devDependencies": {"@repo/testing": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "jsdom": "^26.1.0", "tailwindcss": "^4.1.7", "typescript": "^5.8.3", "vitest": "^3.1.4"}}