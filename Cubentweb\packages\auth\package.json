{"name": "@repo/auth", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@clerk/nextjs": "^6.20.0", "@clerk/themes": "^2.2.46", "@t3-oss/env-nextjs": "^0.13.4", "next-themes": "^0.4.6", "react": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.25.28"}, "devDependencies": {"@clerk/types": "^4.59.1", "@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3"}}